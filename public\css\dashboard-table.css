/* Dashboard Table Styling - <PERSON><PERSON><PERSON> b<PERSON>o hiển thị đầy đủ */

/* Dashboard table container - đơn giản và rõ ràng */
.table-container {
    width: 100% !important;
    overflow-x: auto !important;
    border-radius: 0.375rem !important;
    border: 1px solid #e2e8f0 !important;
    background: white !important;
}

/* Dashboard table styling */
.table {
    margin-bottom: 0 !important;
    font-size: 0.875rem !important;
    width: 100% !important;
}

.table th {
    background-color: #f8fafc !important;
    font-weight: 600 !important;
    color: #374151 !important;
    border-bottom: 2px solid #e5e7eb !important;
    padding: 0.75rem !important;
    white-space: nowrap !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 10 !important;
}

.table td {
    padding: 0.75rem !important;
    vertical-align: middle !important;
    border-bottom: 1px solid #e5e7eb !important;
    background: white !important;
}

/* Button groups trong dashboard - ĐẶC BIỆT CHÚ Ý */
.btn-group {
    display: flex !important;
    flex-wrap: nowrap !important;
    gap: 4px !important;
    align-items: center !important;
}

.btn-group .btn {
    font-size: 0.875rem !important;
    padding: 0.375rem 0.75rem !important;
    margin: 0 !important;
    border-radius: 0.375rem !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 36px !important;
    height: 34px !important;
    border: 1px solid !important;
    background: white !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
}

/* NÚT NHẮC NHỞ - MÀU VÀNG NỔI BẬT */
.btn-outline-warning.reminder-btn,
.btn-outline-warning {
    color: #f59e0b !important;
    border-color: #f59e0b !important;
    background-color: white !important;
    font-weight: 600 !important;
}

.btn-outline-warning:hover,
.btn-outline-warning.reminder-btn:hover {
    background-color: #f59e0b !important;
    color: white !important;
    border-color: #f59e0b !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3) !important;
}

/* NÚT NHẮC NHỞ DỊCH VỤ ĐÃ HẾT HẠN - CỰC KỲ NỔI BẬT */
.btn.expired-reminder-btn,
.btn-danger.expired-reminder-btn {
    background-color: #dc3545 !important;
    border-color: #c82333 !important;
    color: white !important;
    font-weight: 700 !important;
    animation: pulse-red 2s infinite !important;
    box-shadow: 0 0 10px rgba(220, 53, 69, 0.5) !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.btn.expired-reminder-btn:hover {
    background-color: #c82333 !important;
    border-color: #bd2130 !important;
    transform: scale(1.05) !important;
    box-shadow: 0 0 15px rgba(220, 53, 69, 0.8) !important;
}

/* NÚT NHẮC LẠI */
.btn.remind-again-btn {
    background-color: #dc3545 !important;
    border-color: #c82333 !important;
    color: white !important;
    font-weight: 600 !important;
}

.btn.remind-again-btn:hover {
    background-color: #c82333 !important;
    border-color: #bd2130 !important;
    transform: translateY(-1px) !important;
}

/* NÚT ĐÃ NHẮC - MÀU XANH */
.btn-success {
    background-color: #10b981 !important;
    border-color: #10b981 !important;
    color: white !important;
    font-weight: 600 !important;
}

.btn-success:disabled {
    opacity: 0.7 !important;
    cursor: not-allowed !important;
}

/* CÁC NÚT KHÁC */
.btn-outline-primary {
    color: #3b82f6 !important;
    border-color: #3b82f6 !important;
    background-color: white !important;
}

.btn-outline-primary:hover {
    background-color: #3b82f6 !important;
    color: white !important;
}

.btn-outline-info {
    color: #06b6d4 !important;
    border-color: #06b6d4 !important;
    background-color: white !important;
}

.btn-outline-info:hover {
    background-color: #06b6d4 !important;
    color: white !important;
}

/* Badge styling - RÕ RÀNG HƠN */
.badge {
    font-size: 0.75rem !important;
    padding: 0.35rem 0.65rem !important;
    font-weight: 600 !important;
    border-radius: 0.375rem !important;
}

.badge.bg-warning {
    background-color: #f59e0b !important;
    color: white !important;
}

.badge.bg-success {
    background-color: #10b981 !important;
    color: white !important;
}

.badge.bg-danger {
    background-color: #ef4444 !important;
    color: white !important;
}

.badge.bg-info {
    background-color: #3b82f6 !important;
    color: white !important;
}

/* ANIMATION CHO NÚT KHẨN CẤP */
@keyframes pulse-red {
    0% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

@keyframes blink {
    0%,
    50% {
        opacity: 1;
    }
    51%,
    100% {
        opacity: 0.7;
    }
}

/* TABLE STYLING CHO DỊCH VỤ ĐÃ HẾT HẠN */
.table-danger-light {
    background-color: rgba(220, 53, 69, 0.1) !important;
}

.table-danger {
    background-color: rgba(220, 53, 69, 0.2) !important;
}

.card.border-danger {
    border-color: #dc3545 !important;
    border-width: 2px !important;
}

.bg-danger.bg-opacity-10 {
    background-color: rgba(220, 53, 69, 0.1) !important;
}

/* Tooltip styling */
[data-bs-toggle="tooltip"] {
    cursor: pointer !important;
}

/* Debug styling */
.debug-info {
    background: #fef3c7 !important;
    border: 1px solid #f59e0b !important;
    color: #92400e !important;
    padding: 0.5rem !important;
    border-radius: 0.375rem !important;
    font-size: 0.75rem !important;
}

/* Responsive cho dashboard table */
@media (max-width: 768px) {
    .table-container {
        font-size: 0.75rem !important;
    }

    .btn-group .btn {
        padding: 0.25rem 0.5rem !important;
        font-size: 0.75rem !important;
        min-width: 32px !important;
        height: 30px !important;
    }

    .table th,
    .table td {
        padding: 0.5rem !important;
    }
}
