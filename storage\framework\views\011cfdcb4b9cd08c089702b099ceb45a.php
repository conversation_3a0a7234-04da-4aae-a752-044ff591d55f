<?php $__env->startSection('title', '<PERSON>uản lý nhà cung cấp'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .nav-tabs .nav-link {
        border: none;
        border-radius: 10px 10px 0 0;
        color: #6c757d;
        font-weight: 600;
        padding: 1rem 1.5rem;
        margin-right: 0.5rem;
        background: #f8f9fc;
        transition: all 0.3s ease;
    }

    .nav-tabs .nav-link:hover {
        background: #e3e6f0;
        color: #495057;
    }

    .nav-tabs .nav-link.active {
        background: #4e73df;
        color: white;
        border-color: #4e73df;
    }

    .tab-content {
        background: white;
        border-radius: 0 15px 15px 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        min-height: 500px;
    }

    .stats-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease-in-out;
    }

    .stats-card:hover {
        transform: translateY(-2px);
    }

    .icon-wrapper {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
        background-color: #f8f9fa;
    }

    .table td {
        vertical-align: middle;
    }

    .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        border-radius: 6px;
    }

    .search-box {
        border-radius: 10px;
        border: 1px solid #e3e6f0;
        padding: 0.75rem 1rem;
    }

    .search-box:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }

    .filter-card {
        background: #f8f9fc;
        border: 1px solid #e3e6f0;
        border-radius: 10px;
    }

    .tab-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e3e6f0;
        background: #f8f9fc;
        border-radius: 15px 15px 0 0;
    }

    .tab-body {
        padding: 1.5rem;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div>
            <h1 class="h3 mb-2 text-gray-800 fw-bold">
                <i class="fas fa-users-cog me-2 text-primary"></i>
                Quản lý nhà cung cấp
            </h1>
            <p class="mb-0 text-muted">Quản lý danh sách nhà cung cấp hiện tại và tiềm năng</p>
        </div>
    </div>

    <!-- Navigation Tabs -->
    <ul class="nav nav-tabs mb-0" id="supplierTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="current-tab" data-bs-toggle="tab" data-bs-target="#current" 
                    type="button" role="tab" aria-controls="current" aria-selected="true">
                <i class="fas fa-building me-2"></i>
                Nhà cung cấp hiện tại
                <span class="badge bg-light text-dark ms-2"><?php echo e($currentSuppliersCount ?? 0); ?></span>
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="potential-tab" data-bs-toggle="tab" data-bs-target="#potential"
                    type="button" role="tab" aria-controls="potential" aria-selected="false">
                <i class="fas fa-users me-2"></i>
                Nhà cung cấp tiềm năng
                <span class="badge bg-light text-dark ms-2"><?php echo e($potentialSuppliersCount ?? 0); ?></span>
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="statistics-tab" data-bs-toggle="tab" data-bs-target="#statistics"
                    type="button" role="tab" aria-controls="statistics" aria-selected="false">
                <i class="fas fa-chart-bar me-2"></i>
                Thống kê
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="supplierTabContent">
        <!-- Current Suppliers Tab -->
        <div class="tab-pane fade show active" id="current" role="tabpanel" aria-labelledby="current-tab">
            <div class="tab-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1 fw-bold text-primary">
                            <i class="fas fa-building me-2"></i>
                            Nhà cung cấp hiện tại
                        </h5>
                        <p class="mb-0 text-muted">Danh sách các nhà cung cấp đang hợp tác</p>
                    </div>
                    <a href="<?php echo e(route('admin.suppliers.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Thêm nhà cung cấp
                    </a>
                </div>
            </div>
            <div class="tab-body">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Đang tải...</span>
                    </div>
                    <p class="mt-3 text-muted">Đang tải danh sách nhà cung cấp hiện tại...</p>
                </div>
            </div>
        </div>

        <!-- Potential Suppliers Tab -->
        <div class="tab-pane fade" id="potential" role="tabpanel" aria-labelledby="potential-tab">
            <div class="tab-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1 fw-bold text-primary">
                            <i class="fas fa-users me-2"></i>
                            Nhà cung cấp tiềm năng
                        </h5>
                        <p class="mb-0 text-muted">Danh sách các nhà cung cấp có tiềm năng hợp tác</p>
                    </div>
                    <a href="<?php echo e(route('admin.potential-suppliers.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Thêm nhà cung cấp tiềm năng
                    </a>
                </div>
            </div>
            <div class="tab-body">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Đang tải...</span>
                    </div>
                    <p class="mt-3 text-muted">Đang tải danh sách nhà cung cấp tiềm năng...</p>
                </div>
            </div>
        </div>

        <!-- Statistics Tab -->
        <div class="tab-pane fade" id="statistics" role="tabpanel" aria-labelledby="statistics-tab">
            <div class="tab-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1 fw-bold text-primary">
                            <i class="fas fa-chart-bar me-2"></i>
                            Thống kê nhà cung cấp
                        </h5>
                        <p class="mb-0 text-muted">Báo cáo và thống kê tổng quan về nhà cung cấp</p>
                    </div>
                </div>
            </div>
            <div class="tab-body">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Đang tải...</span>
                    </div>
                    <p class="mt-3 text-muted">Đang tải thống kê...</p>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const currentTab = document.getElementById('current-tab');
    const potentialTab = document.getElementById('potential-tab');
    const statisticsTab = document.getElementById('statistics-tab');
    const currentContent = document.getElementById('current');
    const potentialContent = document.getElementById('potential');
    const statisticsContent = document.getElementById('statistics');

    // Load current suppliers when tab is activated
    currentTab.addEventListener('shown.bs.tab', function() {
        if (!currentContent.dataset.loaded) {
            loadCurrentSuppliers();
            currentContent.dataset.loaded = 'true';
        }
    });

    // Load potential suppliers when tab is activated
    potentialTab.addEventListener('shown.bs.tab', function() {
        if (!potentialContent.dataset.loaded) {
            loadPotentialSuppliers();
            potentialContent.dataset.loaded = 'true';
        }
    });

    // Load statistics when tab is activated
    statisticsTab.addEventListener('shown.bs.tab', function() {
        if (!statisticsContent.dataset.loaded) {
            loadStatistics();
            statisticsContent.dataset.loaded = 'true';
        }
    });

    // Load current suppliers by default
    loadCurrentSuppliers();
    currentContent.dataset.loaded = 'true';

    function loadCurrentSuppliers() {
        fetch('<?php echo e(route("admin.suppliers.original")); ?>', {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.text())
        .then(html => {
            // Extract the content we need from the response
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // Get the main content (everything after the header)
            const content = doc.querySelector('.container-fluid');
            if (content) {
                // Remove the header part and keep only the content after stats
                const statsCards = content.querySelector('.row.mb-4');
                const searchFilter = content.querySelector('.card.filter-card');
                const suppliersTable = content.querySelector('.card.shadow');

                let newContent = '';
                if (statsCards) newContent += statsCards.outerHTML;
                if (searchFilter) newContent += searchFilter.outerHTML;
                if (suppliersTable) newContent += suppliersTable.outerHTML;
                
                currentContent.querySelector('.tab-body').innerHTML = newContent || '<div class="text-center py-5"><p class="text-muted">Không thể tải dữ liệu</p></div>';
            }
        })
        .catch(error => {
            console.error('Error loading current suppliers:', error);
            currentContent.querySelector('.tab-body').innerHTML = '<div class="text-center py-5"><p class="text-danger">Lỗi khi tải dữ liệu</p></div>';
        });
    }

    function loadPotentialSuppliers() {
        fetch('<?php echo e(route("admin.potential-suppliers.index")); ?>', {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.text())
        .then(html => {
            // Extract the content we need from the response
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            
            // Get the main content (everything after the header)
            const content = doc.querySelector('.container-fluid');
            if (content) {
                // Remove the header part and keep only the content after stats
                const statsCards = content.querySelector('.row.mb-4');
                const searchFilter = content.querySelector('.card.filter-card');
                const suppliersTable = content.querySelector('.card.shadow');
                
                let newContent = '';
                if (statsCards) newContent += statsCards.outerHTML;
                if (searchFilter) newContent += searchFilter.outerHTML;
                if (suppliersTable) newContent += suppliersTable.outerHTML;
                
                potentialContent.querySelector('.tab-body').innerHTML = newContent || '<div class="text-center py-5"><p class="text-muted">Không thể tải dữ liệu</p></div>';
            }
        })
        .catch(error => {
            console.error('Error loading potential suppliers:', error);
            potentialContent.querySelector('.tab-body').innerHTML = '<div class="text-center py-5"><p class="text-danger">Lỗi khi tải dữ liệu</p></div>';
        });
    }

    function loadStatistics() {
        fetch('<?php echo e(route("admin.suppliers.statistics")); ?>', {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.text())
        .then(html => {
            // Extract the content we need from the response
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // Get the main content (everything after the header)
            const content = doc.querySelector('.container-fluid');
            if (content) {
                // Remove the header part and keep only the content
                const header = content.querySelector('.d-sm-flex.align-items-center');
                if (header) {
                    header.remove();
                }

                statisticsContent.querySelector('.tab-body').innerHTML = content.innerHTML || '<div class="text-center py-5"><p class="text-muted">Không thể tải dữ liệu thống kê</p></div>';
            }
        })
        .catch(error => {
            console.error('Error loading statistics:', error);
            statisticsContent.querySelector('.tab-body').innerHTML = '<div class="text-center py-5"><p class="text-danger">Lỗi khi tải thống kê</p></div>';
        });
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\truycuuthongtin\resources\views/admin/suppliers/combined-index.blade.php ENDPATH**/ ?>