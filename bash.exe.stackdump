Stack trace:
Frame         Function      Args
0007FFFF9F80  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8E80) msys-2.0.dll+0x2118E
0007FFFF9F80  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFA258) msys-2.0.dll+0x69BA
0007FFFF9F80  0002100469F2 (00021028DF99, 0007FFFF9E38, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9F80  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9F80  00021006A545 (0007FFFF9F90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFA260  00021006B9A5 (0007FFFF9F90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE0AC20000 ntdll.dll
7FFE08DF0000 KERNEL32.DLL
7FFE083C0000 KERNELBASE.dll
7FFE01FF0000 apphelp.dll
7FFE09B30000 USER32.dll
7FFE08850000 win32u.dll
7FFE09500000 GDI32.dll
7FFE08880000 gdi32full.dll
7FFE08040000 msvcp_win.dll
7FFE07EF0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE0A9F0000 advapi32.dll
7FFE0A450000 msvcrt.dll
7FFE0A740000 sechost.dll
7FFE0A8B0000 RPCRT4.dll
7FFE07470000 CRYPTBASE.DLL
7FFE087B0000 bcryptPrimitives.dll
7FFE08CB0000 IMM32.DLL
