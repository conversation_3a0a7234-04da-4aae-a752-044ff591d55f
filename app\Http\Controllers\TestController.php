<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PotentialSupplier;
use App\Models\Supplier;

class TestController extends Controller
{
    public function testPotentialSuppliers()
    {
        $potentialSuppliers = PotentialSupplier::with('services')->get();
        $currentSuppliers = Supplier::with('products')->get();

        return response()->json([
            'potential_suppliers_count' => $potentialSuppliers->count(),
            'current_suppliers_count' => $currentSuppliers->count(),
            'potential_suppliers' => $potentialSuppliers->map(function ($supplier) {
                return [
                    'id' => $supplier->id,
                    'code' => $supplier->supplier_code,
                    'name' => $supplier->supplier_name,
                    'priority' => $supplier->priority,
                    'services_count' => $supplier->services->count(),
                    'total_estimated_value' => $supplier->total_estimated_value
                ];
            })
        ]);
    }

    public function testSuppliersAjax()
    {
        try {
            // Test current suppliers route
            $currentSuppliersUrl = route('admin.suppliers.original');
            $potentialSuppliersUrl = route('admin.potential-suppliers.index');

            return response()->json([
                'current_suppliers_url' => $currentSuppliersUrl,
                'potential_suppliers_url' => $potentialSuppliersUrl,
                'auth_check' => auth()->guard('admin')->check(),
                'user' => auth()->guard('admin')->user() ? auth()->guard('admin')->user()->name : null,
                'current_suppliers_count' => \App\Models\Supplier::count(),
                'potential_suppliers_count' => \App\Models\PotentialSupplier::count(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    public function testAuthCheck()
    {
        return response()->json([
            'auth_check' => auth()->guard('admin')->check(),
            'user' => auth()->guard('admin')->user() ? auth()->guard('admin')->user()->name : null,
            'session_id' => session()->getId(),
            'message' => 'Auth check from inside middleware'
        ]);
    }
}
