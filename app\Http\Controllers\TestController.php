<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PotentialSupplier;
use App\Models\Supplier;

class TestController extends Controller
{
    public function testPotentialSuppliers()
    {
        $potentialSuppliers = PotentialSupplier::with('services')->get();
        $currentSuppliers = Supplier::with('products')->get();

        return response()->json([
            'potential_suppliers_count' => $potentialSuppliers->count(),
            'current_suppliers_count' => $currentSuppliers->count(),
            'potential_suppliers' => $potentialSuppliers->map(function ($supplier) {
                return [
                    'id' => $supplier->id,
                    'code' => $supplier->supplier_code,
                    'name' => $supplier->supplier_name,
                    'priority' => $supplier->priority,
                    'services_count' => $supplier->services->count(),
                    'total_estimated_value' => $supplier->total_estimated_value
                ];
            })
        ]);
    }
}
