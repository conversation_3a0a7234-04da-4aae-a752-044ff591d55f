

<?php $__env->startSection('title', 'Chi tiết tài khoản dùng chung'); ?>
<?php $__env->startSection('page-title', 'Chi tiết tài khoản dùng chung'); ?>

<?php $__env->startSection('content'); ?>
<!-- Thông báo -->
<?php if(session('success')): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?php echo e(session('success')); ?>

    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if(session('error')): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-circle me-2"></i>
    <?php echo e(session('error')); ?>

    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">Chi tiết: <?php echo e($email); ?></h5>
                        <small class="text-muted">Dịch vụ sử dụng chung email này</small>
                    </div>
                    <div class="d-flex gap-1">
                        <a href="<?php echo e(route('admin.shared-accounts.edit', $email)); ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> Sửa
                        </a>
                        <a href="<?php echo e(route('admin.shared-accounts.index')); ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <!-- Thống kê tài khoản -->
                <div class="row mb-4">
                    <div class="col-6 col-md-2">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4 class="mb-1"><?php echo e($stats['total_services']); ?></h4>
                                <small>Tổng dịch vụ</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-md-2">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h4 class="mb-1"><?php echo e($stats['unique_customers']); ?></h4>
                                <small>Khách hàng</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-md-2">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h4 class="mb-1"><?php echo e($stats['active_services']); ?></h4>
                                <small>Hoạt động</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-md-2">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h4 class="mb-1"><?php echo e($stats['expiring_soon']); ?></h4>
                                <small>Sắp hết hạn</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-md-2">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h4 class="mb-1"><?php echo e($stats['expired_services']); ?></h4>
                                <small>Đã hết hạn</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-md-2 d-none d-md-block">
                        <div class="card bg-secondary text-white">
                            <div class="card-body text-center">
                                <h4 class="mb-1"><?php echo e($services->count()); ?></h4>
                                <small>Hiển thị</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Thông tin tài khoản dùng chung -->
                <?php
                    $firstService = $services->first();
                ?>
                
                <?php if($firstService): ?>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-key me-2"></i>Thông tin đăng nhập</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-2">
                                    <strong>Email:</strong> <?php echo e($email); ?>

                                </div>
                                <div class="mb-2">
                                    <strong>Mật khẩu:</strong> 
                                    <?php if($firstService->login_password): ?>
                                        <span class="password-field" data-password="<?php echo e($firstService->login_password); ?>">••••••••</span>
                                        <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="togglePasswordView(this)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    <?php else: ?>
                                        <span class="text-muted">Chưa có</span>
                                    <?php endif; ?>
                                </div>
                                <div class="mb-2">
                                    <strong>Hết hạn mật khẩu:</strong> 
                                    <?php if($firstService->password_expires_at): ?>
                                        <span class="badge bg-<?php echo e($firstService->password_expires_at->isPast() ? 'danger' : 'info'); ?>">
                                            <?php echo e($firstService->password_expires_at->format('d/m/Y H:i')); ?>

                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">Không giới hạn</span>
                                    <?php endif; ?>
                                </div>
                                <div class="mb-2">
                                    <strong>Đã chia sẻ:</strong>
                                    <?php if($firstService->is_password_shared): ?>
                                        <span class="badge bg-success">Có</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">Chưa</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Xác thực 2 yếu tố</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-2">
                                    <strong>Mã 2FA:</strong> 
                                    <?php if($firstService->two_factor_code): ?>
                                        <span class="password-field" data-password="<?php echo e($firstService->two_factor_code); ?>">••••••••</span>
                                        <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="togglePasswordView(this)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    <?php else: ?>
                                        <span class="text-muted">Chưa có</span>
                                    <?php endif; ?>
                                </div>
                                <div class="mb-2">
                                    <strong>Cập nhật lần cuối:</strong> 
                                    <?php if($firstService->two_factor_updated_at): ?>
                                        <?php echo e($firstService->two_factor_updated_at->format('d/m/Y H:i')); ?>

                                    <?php else: ?>
                                        <span class="text-muted">Chưa có</span>
                                    <?php endif; ?>
                                </div>
                                <div class="mb-2">
                                    <strong>Mã khôi phục:</strong> 
                                    <?php if($firstService->recovery_codes && count($firstService->recovery_codes) > 0): ?>
                                        <span class="badge bg-info"><?php echo e(count($firstService->recovery_codes)); ?> mã</span>
                                        <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="showRecoveryCodes(<?php echo e(json_encode($firstService->recovery_codes)); ?>)">
                                            <i class="fas fa-eye"></i> Xem
                                        </button>
                                    <?php else: ?>
                                        <span class="text-muted">Chưa có</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if($firstService->shared_account_notes || $firstService->customer_instructions): ?>
                <div class="row mb-4">
                    <?php if($firstService->shared_account_notes): ?>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-sticky-note me-2"></i>Ghi chú nội bộ</h6>
                            </div>
                            <div class="card-body">
                                <?php echo e($firstService->shared_account_notes); ?>

                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if($firstService->customer_instructions): ?>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-paper-plane me-2"></i>Hướng dẫn khách hàng</h6>
                            </div>
                            <div class="card-body">
                                <?php echo nl2br(e($firstService->customer_instructions)); ?>

                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                <?php endif; ?>

                <?php if($stats['unique_customers'] > 1): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Cảnh báo:</strong> Tài khoản này được sử dụng bởi <?php echo e($stats['unique_customers']); ?> khách hàng khác nhau. 
                    Vui lòng kiểm tra và xử lý để tránh xung đột.
                </div>
                <?php endif; ?>

                <!-- Bảng dịch vụ -->
                <div class="table-responsive">
                    <!-- Responsive info alert -->
                    <div class="alert alert-info alert-sm d-lg-none mb-2">
                        <i class="fas fa-info-circle me-1"></i>
                        <small>Một số cột (mật khẩu, thông tin chi tiết) được ẩn trên màn hình nhỏ để tối ưu hiển thị.</small>
                    </div>
                    
                    <table class="table table-striped table-hover table-sm">
                        <thead class="table-dark">
                            <tr>
                                <th style="min-width: 180px;">Khách hàng</th>
                                <th style="min-width: 120px;">Gói dịch vụ</th>
                                <th class="d-none-lg" style="min-width: 150px;">Mật khẩu</th>
                                <th class="d-none-xl" style="min-width: 100px;">Kích hoạt</th>
                                <th style="min-width: 120px;">Hết hạn</th>
                                <th style="min-width: 100px;">Trạng thái</th>
                                <th class="d-none-md" style="min-width: 100px;">Nhắc nhở</th>
                                <th class="d-none-lg" style="min-width: 120px;">Người PC</th>
                                <th style="min-width: 80px;">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $isExpired = $service->expires_at && $service->expires_at->isPast();
                                $isExpiring = $service->expires_at && $service->expires_at->isFuture() && $service->expires_at->diffInDays(now()) <= 7;
                                $statusClass = $isExpired ? 'table-danger' : ($isExpiring ? 'table-warning' : '');
                            ?>
                            <tr class="<?php echo e($statusClass); ?>">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div>
                                            <strong><?php echo e($service->customer->name); ?></strong>
                                            <?php if($service->customer->customer_code): ?>
                                                <span class="badge bg-light text-dark ms-2" 
                                                      title="Mã khách hàng">
                                                    <i class="fas fa-id-badge me-1"></i><?php echo e($service->customer->customer_code); ?>

                                                </span>
                                            <?php endif; ?>
                                            <br><small class="text-muted">
                                                <i class="fas fa-phone me-1"></i><?php echo e($service->customer->phone); ?>

                                            </small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?php echo e($service->servicePackage->name); ?></span>
                                </td>
                                <td class="d-none-lg">
                                    <div class="d-flex align-items-center">
                                        <code id="password-<?php echo e($service->id); ?>" style="display: none;"><?php echo e($service->login_password); ?></code>
                                        <span id="masked-<?php echo e($service->id); ?>"><?php echo e(str_repeat('*', strlen($service->login_password))); ?></span>
                                        <button type="button" class="btn btn-sm btn-outline-secondary ms-2" 
                                                onclick="togglePassword(<?php echo e($service->id); ?>)">
                                            <i class="fas fa-eye" id="eye-<?php echo e($service->id); ?>"></i>
                                        </button>
                                    </div>
                                </td>
                                <td class="d-none-xl">
                                    <?php if($service->activated_at): ?>
                                        <small><?php echo e($service->activated_at->format('d/m/Y')); ?></small>
                                    <?php else: ?>
                                        <span class="text-muted">Chưa kích hoạt</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($service->expires_at): ?>
                                        <div>
                                            <?php echo e($service->expires_at->format('d/m/Y')); ?>

                                            <?php if($isExpired): ?>
                                                <br><small class="text-danger">Đã hết hạn <?php echo e($service->expires_at->diffForHumans()); ?></small>
                                            <?php elseif($isExpiring): ?>
                                                <br><small class="text-warning">Còn <?php echo e($service->expires_at->diffInDays(now())); ?> ngày</small>
                                            <?php else: ?>
                                                <br><small class="text-success">Còn <?php echo e($service->expires_at->diffInDays(now())); ?> ngày</small>
                                            <?php endif; ?>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">Không giới hạn</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($service->status === 'active'): ?>
                                        <span class="badge bg-success">Hoạt động</span>
                                    <?php elseif($service->status === 'inactive'): ?>
                                        <span class="badge bg-secondary">Không hoạt động</span>
                                    <?php elseif($service->status === 'suspended'): ?>
                                        <span class="badge bg-warning">Tạm dừng</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Hết hạn</span>
                                    <?php endif; ?>
                                </td>
                                <td class="d-none-md">
                                    <?php if($service->reminder_sent): ?>
                                        <div>
                                            <span class="badge bg-info">Đã nhắc</span>
                                            <br><small class="text-muted"><?php echo e($service->reminder_sent_at->format('d/m')); ?></small>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">Chưa</span>
                                    <?php endif; ?>
                                </td>
                                <td class="d-none-lg">
                                    <?php if($service->assignedBy): ?>
                                        <?php echo e($service->assignedBy->name); ?>

                                    <?php else: ?>
                                        <span class="text-muted">N/A</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                                data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li>
                                                <a class="dropdown-item" href="<?php echo e(route('admin.customer-services.index', ['customer_id' => $service->customer_id])); ?>">
                                                    <i class="fas fa-user me-2"></i>
                                                    Xem khách hàng
                                                </a>
                                            </li>
                                            <?php if($isExpiring || $isExpired): ?>
                                            <li>
                                                <form method="POST" action="<?php echo e(route('admin.customer-services.mark-reminded', $service->id)); ?>" class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit" class="dropdown-item">
                                                        <i class="fas fa-bell me-2"></i>
                                                        Đánh dấu đã nhắc
                                                    </button>
                                                </form>
                                            </li>
                                            <?php endif; ?>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <a class="dropdown-item text-primary"
                                                   href="mailto:<?php echo e($service->customer->email); ?>?subject=Thông báo gia hạn dịch vụ <?php echo e($service->servicePackage->name); ?>">
                                                    <i class="fas fa-envelope me-2"></i>
                                                    Gửi email
                                                </a>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <button type="button"
                                                        class="dropdown-item text-danger"
                                                        onclick="confirmDeleteService('<?php echo e($service->customer->name); ?> - <?php echo e($service->servicePackage->name); ?>', '<?php echo e(route('admin.customer-services.destroy', $service)); ?>')">
                                                    <i class="fas fa-trash me-2"></i>
                                                    Xóa dịch vụ
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <?php if($services->isEmpty()): ?>
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Không có dịch vụ nào</h5>
                    <p class="text-muted">Không tìm thấy dịch vụ nào sử dụng email này.</p>
                </div>
                <?php endif; ?>

                <!-- Ghi chú và hướng dẫn -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Ghi chú quan trọng</h6>
                            </div>
                            <div class="card-body">
                                <ul class="mb-0">
                                    <li>Các dịch vụ sử dụng chung email có thể gây xung đột</li>
                                    <li>Nên tách riêng email cho từng khách hàng</li>
                                    <li>Kiểm tra thường xuyên trạng thái các dịch vụ</li>
                                    <li>Liên hệ khách hàng khi có dịch vụ sắp hết hạn</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Thông tin liên hệ</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>Email tài khoản:</strong> <?php echo e($email); ?></p>
                                <p><strong>Khách hàng sử dụng:</strong></p>
                                <ul class="mb-0">
                                    <?php $__currentLoopData = $services->unique('customer_id'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="mb-2">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div>
                                                <strong><?php echo e($service->customer->name); ?></strong>
                                                <?php if($service->customer->phone): ?>
                                                    <br><small class="text-muted">
                                                        <i class="fas fa-phone me-1"></i><?php echo e($service->customer->phone); ?>

                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                            <?php if($service->customer->customer_code): ?>
                                                <span class="badge bg-primary" title="Mã khách hàng">
                                                    <i class="fas fa-id-badge me-1"></i><?php echo e($service->customer->customer_code); ?>

                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(serviceId) {
    const passwordField = document.getElementById('password-' + serviceId);
    const maskedField = document.getElementById('masked-' + serviceId);
    const eyeIcon = document.getElementById('eye-' + serviceId);
    
    if (passwordField.style.display === 'none') {
        passwordField.style.display = 'inline';
        maskedField.style.display = 'none';
        eyeIcon.className = 'fas fa-eye-slash';
    } else {
        passwordField.style.display = 'none';
        maskedField.style.display = 'inline';
        eyeIcon.className = 'fas fa-eye';
    }
}

// Hàm toggle hiển thị password cho thông tin tài khoản dùng chung
function togglePasswordView(button) {
    const passwordField = button.previousElementSibling;
    const icon = button.querySelector('i');
    
    if (passwordField.textContent === '••••••••') {
        passwordField.textContent = passwordField.getAttribute('data-password');
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordField.textContent = '••••••••';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Hàm hiển thị mã khôi phục
function showRecoveryCodes(codes) {
    const codesText = codes.join('\n');
    alert('Mã khôi phục:\n\n' + codesText);
}

// Hàm xác nhận xóa dịch vụ
function confirmDeleteService(serviceName, deleteUrl) {
    if (confirm('Bạn có chắc chắn muốn xóa dịch vụ "' + serviceName + '"?\n\nHành động này không thể hoàn tác!')) {
        // Tạo form để gửi DELETE request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = deleteUrl;

        // Thêm CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';
        form.appendChild(csrfToken);

        // Thêm method DELETE
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        form.appendChild(methodField);

        // Thêm form vào body và submit
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\truycuuthongtin\resources\views/admin/shared-accounts/show.blade.php ENDPATH**/ ?>